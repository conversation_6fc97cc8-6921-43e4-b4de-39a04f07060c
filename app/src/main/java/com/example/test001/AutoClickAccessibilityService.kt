package com.example.test001

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.graphics.Path
import android.graphics.Rect
import android.os.Handler
import android.os.Looper
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo

class AutoClickAccessibilityService : AccessibilityService() {
    
    private val handler = Handler(Looper.getMainLooper())
    private var isEnabled = false
    
    companion object {
        private var instance: AutoClickAccessibilityService? = null
        
        fun enableAutoClick() {
            instance?.isEnabled = true
            FloatingWindowService.addLog("无障碍服务已启用")
        }
        
        fun disableAutoClick() {
            instance?.isEnabled = false
            FloatingWindowService.addLog("无障碍服务已禁用")
        }

        fun isServiceRunning(): Bo<PERSON>an {
            return instance != null
        }

        fun manualCheck() {
            instance?.let {
                FloatingWindowService.addLog("手动触发检测")
                it.handler.post {
                    it.checkAndClickTaobaoButton()
                }
            }
        }
    }
    
    override fun onServiceConnected() {
        super.onServiceConnected()
        instance = this
        FloatingWindowService.addLog("无障碍服务已连接")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        instance = null
    }
    
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (!isEnabled || event == null) return

        // 记录事件信息用于调试
        val packageName = event.packageName?.toString() ?: ""
        FloatingWindowService.addLog("检测到事件: $packageName")

        when (event.eventType) {
            AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> {
                FloatingWindowService.addLog("窗口状态改变: $packageName")
                // 如果是浏览器，延迟检查
                if (isBrowserPackage(packageName)) {
                    handler.postDelayed({
                        checkAndClickTaobaoButton()
                    }, 3000)
                }
            }
            AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
                // 内容改变时也检查
                if (isBrowserPackage(packageName)) {
                    handler.postDelayed({
                        checkAndClickTaobaoButton()
                    }, 2000)
                }
            }
        }
    }

    private fun isBrowserPackage(packageName: String): Boolean {
        return packageName.contains("chrome") ||
               packageName.contains("browser") ||
               packageName.contains("firefox") ||
               packageName.contains("opera") ||
               packageName.contains("edge") ||
               packageName.contains("ucmobile") ||
               packageName.contains("mtt") // QQ浏览器
    }
    
    override fun onInterrupt() {
        // 服务被中断时的处理
    }
    
    private fun checkAndClickTaobaoButton() {
        if (!isEnabled) return

        val rootNode = rootInActiveWindow ?: run {
            FloatingWindowService.addLog("无法获取根节点")
            return
        }

        FloatingWindowService.addLog("开始检查页面内容...")

        // 首先检查是否已经在淘宝app中
        if (isInTaobaoApp(rootNode)) {
            FloatingWindowService.addLog("已在淘宝app中，无需点击")
            rootNode.recycle()
            return
        }

        // 查找"打开淘宝"按钮
        val taobaoButton = findTaobaoButton(rootNode)
        if (taobaoButton != null) {
            FloatingWindowService.addLog("找到打开淘宝按钮，准备点击")
            clickNode(taobaoButton)
            FloatingWindowService.addLog("已点击打开淘宝按钮")
        } else {
            FloatingWindowService.addLog("未找到打开淘宝按钮，尝试查找所有可点击元素")
            findAllClickableElements(rootNode)
        }

        rootNode.recycle()
    }

    private fun findAllClickableElements(node: AccessibilityNodeInfo) {
        // 查找所有可点击的元素，用于调试
        val clickableElements = mutableListOf<String>()
        findClickableElementsRecursive(node, clickableElements)

        if (clickableElements.isNotEmpty()) {
            FloatingWindowService.addLog("找到可点击元素: ${clickableElements.take(3).joinToString(", ")}")

            // 尝试点击包含关键词的元素
            for (element in clickableElements) {
                if (element.contains("淘宝", true) ||
                    element.contains("打开", true) ||
                    element.contains("app", true)) {
                    FloatingWindowService.addLog("尝试点击: $element")
                    // 这里可以添加点击逻辑
                    break
                }
            }
        } else {
            FloatingWindowService.addLog("页面中没有找到可点击元素")
        }
    }

    private fun findClickableElementsRecursive(node: AccessibilityNodeInfo, elements: MutableList<String>) {
        if (node.isClickable) {
            val text = node.text?.toString() ?: ""
            val desc = node.contentDescription?.toString() ?: ""
            val className = node.className?.toString() ?: ""

            if (text.isNotEmpty() || desc.isNotEmpty()) {
                elements.add("$text|$desc|$className")
            }
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                findClickableElementsRecursive(child, elements)
                child.recycle()
            }
        }
    }
    
    private fun isInTaobaoApp(node: AccessibilityNodeInfo): Boolean {
        // 检查包名是否为淘宝相关
        val packageName = node.packageName?.toString() ?: ""
        return packageName.contains("taobao") || 
               packageName.contains("tmall") ||
               packageName.contains("alibaba")
    }
    
    private fun findTaobaoButton(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        // 检查当前节点的文本
        val text = node.text?.toString()?.lowercase() ?: ""
        val contentDesc = node.contentDescription?.toString()?.lowercase() ?: ""
        
        // 查找包含"打开淘宝"、"淘宝"、"打开"等关键词的按钮
        if ((text.contains("打开淘宝") || text.contains("淘宝") || text.contains("打开")) &&
            (node.isClickable || node.className?.contains("Button") == true)) {
            return node
        }
        
        if ((contentDesc.contains("打开淘宝") || contentDesc.contains("淘宝") || contentDesc.contains("打开")) &&
            (node.isClickable || node.className?.contains("Button") == true)) {
            return node
        }
        
        // 递归查找子节点
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                val result = findTaobaoButton(child)
                if (result != null) {
                    child.recycle()
                    return result
                }
                child.recycle()
            }
        }
        
        return null
    }
    
    private fun clickNode(node: AccessibilityNodeInfo) {
        // 尝试直接点击
        if (node.isClickable) {
            node.performAction(AccessibilityNodeInfo.ACTION_CLICK)
            return
        }
        
        // 如果不能直接点击，尝试手势点击
        val rect = Rect()
        node.getBoundsInScreen(rect)
        
        val centerX = rect.centerX().toFloat()
        val centerY = rect.centerY().toFloat()
        
        val path = Path()
        path.moveTo(centerX, centerY)
        
        val gesture = GestureDescription.Builder()
            .addStroke(GestureDescription.StrokeDescription(path, 0, 100))
            .build()
        
        dispatchGesture(gesture, null, null)
    }
}
