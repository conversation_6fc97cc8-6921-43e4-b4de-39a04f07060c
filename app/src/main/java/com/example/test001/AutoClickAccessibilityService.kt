package com.example.test001

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.graphics.Path
import android.graphics.Rect
import android.os.Handler
import android.os.Looper
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo

class AutoClickAccessibilityService : AccessibilityService() {
    
    private val handler = Handler(Looper.getMainLooper())
    private var isEnabled = false
    private var lastCheckTime = 0L
    private var lastLogMessage = ""
    private var lastLogTime = 0L
    
    companion object {
        private var instance: AutoClickAccessibilityService? = null
        
        fun enableAutoClick() {
            instance?.isEnabled = true
            FloatingWindowService.addLog("无障碍服务已启用")
        }
        
        fun disableAutoClick() {
            instance?.isEnabled = false
            FloatingWindowService.addLog("无障碍服务已禁用")
        }

        fun isServiceRunning(): Boolean {
            return instance != null
        }

        fun manualCheck() {
            instance?.let {
                FloatingWindowService.addLog("手动检测中...")
                it.handler.post {
                    it.checkAndClickTaobaoButton()
                }
            }
        }
    }
    
    override fun onServiceConnected() {
        super.onServiceConnected()
        instance = this
        FloatingWindowService.addLog("无障碍服务已连接")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        instance = null
    }
    
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (!isEnabled || event == null) return

        val currentTime = System.currentTimeMillis()
        val packageName = event.packageName?.toString() ?: ""

        when (event.eventType) {
            AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> {
                // 防止重复检查，至少间隔10秒
                if (currentTime - lastCheckTime > 10000) {
                    logOnce("检测到窗口变化，准备检查页面")
                    lastCheckTime = currentTime
                    // 延迟检查，给页面充分加载时间
                    handler.postDelayed({
                        checkAndClickTaobaoButton()
                    }, 6000)
                }
            }
            AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
                // 内容改变时检查频率更低，至少间隔15秒
                if (currentTime - lastCheckTime > 15000) {
                    lastCheckTime = currentTime
                    handler.postDelayed({
                        checkAndClickTaobaoButton()
                    }, 3000)
                }
            }
        }
    }

    private fun logOnce(message: String) {
        val currentTime = System.currentTimeMillis()
        if (message != lastLogMessage || currentTime - lastLogTime > 5000) {
            FloatingWindowService.addLog(message)
            lastLogMessage = message
            lastLogTime = currentTime
        }
    }
    
    override fun onInterrupt() {
        // 服务被中断时的处理
    }
    
    private fun checkAndClickTaobaoButton() {
        if (!isEnabled) return

        try {
            val rootNode = rootInActiveWindow
            if (rootNode == null) {
                FloatingWindowService.addLog("无法获取根节点，可能权限不足")
                // 尝试重新获取
                handler.postDelayed({
                    val retryRoot = rootInActiveWindow
                    if (retryRoot != null) {
                        FloatingWindowService.addLog("重试获取根节点成功")
                        processRootNode(retryRoot)
                    } else {
                        FloatingWindowService.addLog("重试仍无法获取根节点")
                    }
                }, 1000)
                return
            }

            processRootNode(rootNode)

        } catch (e: Exception) {
            FloatingWindowService.addLog("检查过程出错: ${e.message}")
        }
    }

    private fun processRootNode(rootNode: AccessibilityNodeInfo) {
        try {
            logOnce("开始检查页面内容...")

            // 首先检查是否已经在淘宝app中
            if (isInTaobaoApp(rootNode)) {
                logOnce("已在淘宝app中，无需点击")
                return
            }

            // 查找"打开淘宝"按钮
            val taobaoButton = findTaobaoButton(rootNode)
            if (taobaoButton != null) {
                FloatingWindowService.addLog("找到目标按钮，开始点击")
                clickNode(taobaoButton)
            } else {
                logOnce("未找到目标按钮，分析所有可点击元素")
                findAllClickableElements(rootNode)
            }

        } finally {
            rootNode.recycle()
        }
    }

    private fun findAllClickableElements(node: AccessibilityNodeInfo) {
        // 查找所有可点击的元素
        val clickableElements = mutableListOf<AccessibilityNodeInfo>()
        findClickableElementsRecursive(node, clickableElements)

        if (clickableElements.isNotEmpty()) {
            FloatingWindowService.addLog("分析${clickableElements.size}个可点击元素...")

            // 使用评分系统选择最佳按钮
            val scoredElements = clickableElements.map { element ->
                val score = calculateButtonScore(element)
                Pair(element, score)
            }.sortedByDescending { it.second }

            if (scoredElements.isNotEmpty()) {
                val bestElement = scoredElements.first()
                val text = bestElement.first.text?.toString() ?: ""
                val desc = bestElement.first.contentDescription?.toString() ?: ""
                val displayText = if (text.isNotEmpty()) text else desc
                FloatingWindowService.addLog("选择备选按钮: $displayText")
                clickNode(bestElement.first)
            }

            // 清理资源
            clickableElements.forEach { it.recycle() }
        } else {
            logOnce("页面中没有找到可点击元素")
        }
    }

    private fun findClickableElementsRecursive(node: AccessibilityNodeInfo, elements: MutableList<AccessibilityNodeInfo>) {
        if (node.isClickable) {
            val text = node.text?.toString() ?: ""
            val desc = node.contentDescription?.toString() ?: ""

            if (text.isNotEmpty() || desc.isNotEmpty()) {
                // 创建节点的副本，避免回收问题
                elements.add(node)
            }
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                findClickableElementsRecursive(child, elements)
                child.recycle()
            }
        }
    }
    
    private fun isInTaobaoApp(node: AccessibilityNodeInfo): Boolean {
        // 检查包名是否为淘宝相关
        val packageName = node.packageName?.toString() ?: ""
        return packageName.contains("taobao") || 
               packageName.contains("tmall") ||
               packageName.contains("alibaba")
    }
    
    private fun findTaobaoButton(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        // 首先查找最佳匹配的按钮
        val bestButton = findBestTaobaoButton(node)
        if (bestButton != null) {
            return bestButton
        }

        // 如果没找到最佳按钮，查找所有可能的按钮
        return findAnyTaobaoButton(node)
    }

    private fun findBestTaobaoButton(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        val candidates = mutableListOf<AccessibilityNodeInfo>()
        collectTaobaoButtonCandidates(node, candidates)

        if (candidates.isEmpty()) {
            return null
        }

        // 按优先级排序候选按钮
        val sortedCandidates = candidates.sortedWith { a, b ->
            val scoreA = calculateButtonScore(a)
            val scoreB = calculateButtonScore(b)
            scoreB.compareTo(scoreA) // 降序排列
        }

        val bestButton = sortedCandidates.first()
        val text = bestButton.text?.toString() ?: ""
        val desc = bestButton.contentDescription?.toString() ?: ""
        val displayText = if (text.isNotEmpty()) text else desc
        FloatingWindowService.addLog("选择按钮: $displayText")

        // 清理其他候选按钮
        candidates.filter { it != bestButton }.forEach { it.recycle() }

        return bestButton
    }

    private fun collectTaobaoButtonCandidates(node: AccessibilityNodeInfo, candidates: MutableList<AccessibilityNodeInfo>) {
        val text = node.text?.toString() ?: ""
        val contentDesc = node.contentDescription?.toString() ?: ""
        val className = node.className?.toString() ?: ""

        // 检查是否包含淘宝相关关键词
        val keywords = listOf("打开淘宝", "淘宝", "打开", "进入", "启动")
        val hasKeyword = keywords.any { keyword ->
            text.contains(keyword, ignoreCase = true) ||
            contentDesc.contains(keyword, ignoreCase = true)
        }

        // 检查是否是可点击的元素
        val isClickable = node.isClickable ||
                         className.contains("Button", ignoreCase = true) ||
                         className.contains("View", ignoreCase = true)

        if (hasKeyword && isClickable) {
            candidates.add(node)
        }

        // 递归查找子节点
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                collectTaobaoButtonCandidates(child, candidates)
                child.recycle()
            }
        }
    }

    private fun calculateButtonScore(node: AccessibilityNodeInfo): Int {
        var score = 0
        val text = node.text?.toString() ?: ""
        val contentDesc = node.contentDescription?.toString() ?: ""
        val className = node.className?.toString() ?: ""

        // 文本匹配得分
        when {
            text.equals("打开淘宝", ignoreCase = true) -> score += 100
            text.contains("打开淘宝", ignoreCase = true) -> score += 80
            text.equals("淘宝", ignoreCase = true) -> score += 60
            text.contains("淘宝", ignoreCase = true) -> score += 40
            text.contains("打开", ignoreCase = true) -> score += 20
        }

        // 描述匹配得分
        when {
            contentDesc.contains("打开淘宝", ignoreCase = true) -> score += 50
            contentDesc.contains("淘宝", ignoreCase = true) -> score += 30
        }

        // 元素类型得分
        when {
            className.contains("Button", ignoreCase = true) -> score += 30
            node.isClickable -> score += 20
        }

        // 位置得分（下方的按钮通常更重要）
        val rect = android.graphics.Rect()
        node.getBoundsInScreen(rect)
        if (rect.top > 500) { // 如果按钮在屏幕下半部分
            score += 25
        }

        // 大小得分（较大的按钮通常更重要）
        val area = rect.width() * rect.height()
        if (area > 50000) { // 较大的按钮
            score += 15
        }

        return score
    }

    private fun findAnyTaobaoButton(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        // 原来的查找逻辑作为备选
        val text = node.text?.toString() ?: ""
        val contentDesc = node.contentDescription?.toString() ?: ""
        val className = node.className?.toString() ?: ""

        val keywords = listOf("淘宝", "打开")
        val hasKeyword = keywords.any { keyword ->
            text.contains(keyword, ignoreCase = true) ||
            contentDesc.contains(keyword, ignoreCase = true)
        }

        val isClickableElement = node.isClickable ||
                                className.contains("Button", ignoreCase = true) ||
                                className.contains("View", ignoreCase = true)

        if (hasKeyword && isClickableElement) {
            return node
        }

        // 递归查找子节点
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                val result = findAnyTaobaoButton(child)
                if (result != null) {
                    return result
                }
                child.recycle()
            }
        }

        return null
    }
    
    private fun clickNode(node: AccessibilityNodeInfo) {
        try {
            val text = node.text?.toString() ?: ""
            val desc = node.contentDescription?.toString() ?: ""
            val displayText = if (text.isNotEmpty()) text else desc

            FloatingWindowService.addLog("正在点击: $displayText")

            // 方法1: 尝试直接点击
            if (node.isClickable) {
                val success = node.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                if (success) {
                    handler.postDelayed({
                        checkClickResult()
                    }, 1500)
                    return
                }
            }

            // 方法2: 尝试点击父节点
            var parent = node.parent
            var level = 1
            while (parent != null && level <= 3) {
                if (parent.isClickable) {
                    val success = parent.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                    if (success) {
                        parent.recycle()
                        handler.postDelayed({
                            checkClickResult()
                        }, 1500)
                        return
                    }
                }
                val nextParent = parent.parent
                parent.recycle()
                parent = nextParent
                level++
            }
            parent?.recycle()

            // 方法3: 手势点击
            val rect = Rect()
            node.getBoundsInScreen(rect)
            if (!rect.isEmpty) {
                val centerX = rect.centerX().toFloat()
                val centerY = rect.centerY().toFloat()

                val path = Path()
                path.moveTo(centerX, centerY)

                val gesture = GestureDescription.Builder()
                    .addStroke(GestureDescription.StrokeDescription(path, 0, 150))
                    .build()

                dispatchGesture(gesture, object : GestureResultCallback() {
                    override fun onCompleted(gestureDescription: GestureDescription?) {
                        handler.postDelayed({
                            checkClickResult()
                        }, 1500)
                    }

                    override fun onCancelled(gestureDescription: GestureDescription?) {
                        FloatingWindowService.addLog("点击被取消")
                    }
                }, null)
            }

        } catch (e: Exception) {
            FloatingWindowService.addLog("点击失败: ${e.message}")
        }
    }

    private fun checkClickResult() {
        try {
            val currentRoot = rootInActiveWindow
            if (currentRoot != null) {
                if (isInTaobaoApp(currentRoot)) {
                    FloatingWindowService.addLog("✅ 成功跳转到淘宝")
                } else {
                    logOnce("等待跳转...")
                }
                currentRoot.recycle()
            }
        } catch (e: Exception) {
            // 静默处理错误，避免过多日志
        }
    }
}
