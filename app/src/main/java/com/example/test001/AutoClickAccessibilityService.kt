package com.example.test001

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.graphics.Path
import android.graphics.Rect
import android.os.Handler
import android.os.Looper
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo

class AutoClickAccessibilityService : AccessibilityService() {
    
    private val handler = Handler(Looper.getMainLooper())
    private var isEnabled = false
    
    companion object {
        private var instance: AutoClickAccessibilityService? = null
        
        fun enableAutoClick() {
            instance?.isEnabled = true
            FloatingWindowService.addLog("无障碍服务已启用")
        }
        
        fun disableAutoClick() {
            instance?.isEnabled = false
            FloatingWindowService.addLog("无障碍服务已禁用")
        }

        fun isServiceRunning(): Bo<PERSON>an {
            return instance != null
        }

        fun manualCheck() {
            instance?.let {
                FloatingWindowService.addLog("手动触发检测")
                it.handler.post {
                    it.checkAndClickTaobaoButton()
                }
            }
        }
    }
    
    override fun onServiceConnected() {
        super.onServiceConnected()
        instance = this
        FloatingWindowService.addLog("无障碍服务已连接")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        instance = null
    }
    
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (!isEnabled || event == null) return

        // 记录事件信息用于调试
        val packageName = event.packageName?.toString() ?: ""

        when (event.eventType) {
            AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> {
                FloatingWindowService.addLog("窗口改变: $packageName")
                // 延迟检查，给页面充分加载时间
                handler.postDelayed({
                    checkAndClickTaobaoButton()
                }, 6000)
            }
            AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
                // 内容改变时也检查，但频率低一些
                if (System.currentTimeMillis() % 5 == 0L) { // 进一步减少频率
                    handler.postDelayed({
                        checkAndClickTaobaoButton()
                    }, 3000)
                }
            }
        }
    }
    
    override fun onInterrupt() {
        // 服务被中断时的处理
    }
    
    private fun checkAndClickTaobaoButton() {
        if (!isEnabled) return

        try {
            val rootNode = rootInActiveWindow
            if (rootNode == null) {
                FloatingWindowService.addLog("无法获取根节点，可能权限不足")
                // 尝试重新获取
                handler.postDelayed({
                    val retryRoot = rootInActiveWindow
                    if (retryRoot != null) {
                        FloatingWindowService.addLog("重试获取根节点成功")
                        processRootNode(retryRoot)
                    } else {
                        FloatingWindowService.addLog("重试仍无法获取根节点")
                    }
                }, 1000)
                return
            }

            processRootNode(rootNode)

        } catch (e: Exception) {
            FloatingWindowService.addLog("检查过程出错: ${e.message}")
        }
    }

    private fun processRootNode(rootNode: AccessibilityNodeInfo) {
        try {
            FloatingWindowService.addLog("成功获取根节点，开始检查...")

            // 首先检查是否已经在淘宝app中
            if (isInTaobaoApp(rootNode)) {
                FloatingWindowService.addLog("已在淘宝app中，无需点击")
                return
            }

            // 查找"打开淘宝"按钮
            val taobaoButton = findTaobaoButton(rootNode)
            if (taobaoButton != null) {
                FloatingWindowService.addLog("找到打开淘宝按钮，开始点击")
                clickNode(taobaoButton)
            } else {
                FloatingWindowService.addLog("未找到打开淘宝按钮，查找所有按钮")
                findAllClickableElements(rootNode)
            }

        } finally {
            rootNode.recycle()
        }
    }

    private fun findAllClickableElements(node: AccessibilityNodeInfo) {
        // 查找所有可点击的元素
        val clickableElements = mutableListOf<AccessibilityNodeInfo>()
        findClickableElementsRecursive(node, clickableElements)

        if (clickableElements.isNotEmpty()) {
            FloatingWindowService.addLog("找到${clickableElements.size}个可点击元素，分析最佳选择...")

            // 使用评分系统选择最佳按钮
            val scoredElements = clickableElements.map { element ->
                val score = calculateButtonScore(element)
                val text = element.text?.toString() ?: ""
                val desc = element.contentDescription?.toString() ?: ""
                FloatingWindowService.addLog("元素: $text|$desc 得分: $score")
                Pair(element, score)
            }.sortedByDescending { it.second }

            if (scoredElements.isNotEmpty()) {
                val bestElement = scoredElements.first()
                val text = bestElement.first.text?.toString() ?: ""
                val desc = bestElement.first.contentDescription?.toString() ?: ""
                FloatingWindowService.addLog("选择最佳元素点击: $text|$desc (得分: ${bestElement.second})")
                clickNode(bestElement.first)
            }

            // 清理资源
            clickableElements.forEach { it.recycle() }
        } else {
            FloatingWindowService.addLog("页面中没有找到可点击元素")
        }
    }

    private fun findClickableElementsRecursive(node: AccessibilityNodeInfo, elements: MutableList<AccessibilityNodeInfo>) {
        if (node.isClickable) {
            val text = node.text?.toString() ?: ""
            val desc = node.contentDescription?.toString() ?: ""

            if (text.isNotEmpty() || desc.isNotEmpty()) {
                // 创建节点的副本，避免回收问题
                elements.add(node)
            }
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                findClickableElementsRecursive(child, elements)
                child.recycle()
            }
        }
    }
    
    private fun isInTaobaoApp(node: AccessibilityNodeInfo): Boolean {
        // 检查包名是否为淘宝相关
        val packageName = node.packageName?.toString() ?: ""
        return packageName.contains("taobao") || 
               packageName.contains("tmall") ||
               packageName.contains("alibaba")
    }
    
    private fun findTaobaoButton(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        // 首先查找最佳匹配的按钮
        val bestButton = findBestTaobaoButton(node)
        if (bestButton != null) {
            return bestButton
        }

        // 如果没找到最佳按钮，查找所有可能的按钮
        return findAnyTaobaoButton(node)
    }

    private fun findBestTaobaoButton(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        val candidates = mutableListOf<AccessibilityNodeInfo>()
        collectTaobaoButtonCandidates(node, candidates)

        if (candidates.isEmpty()) {
            return null
        }

        // 按优先级排序候选按钮
        val sortedCandidates = candidates.sortedWith { a, b ->
            val scoreA = calculateButtonScore(a)
            val scoreB = calculateButtonScore(b)
            scoreB.compareTo(scoreA) // 降序排列
        }

        val bestButton = sortedCandidates.first()
        val text = bestButton.text?.toString() ?: ""
        val desc = bestButton.contentDescription?.toString() ?: ""
        FloatingWindowService.addLog("选择最佳按钮: $text|$desc (得分: ${calculateButtonScore(bestButton)})")

        // 清理其他候选按钮
        candidates.filter { it != bestButton }.forEach { it.recycle() }

        return bestButton
    }

    private fun collectTaobaoButtonCandidates(node: AccessibilityNodeInfo, candidates: MutableList<AccessibilityNodeInfo>) {
        val text = node.text?.toString() ?: ""
        val contentDesc = node.contentDescription?.toString() ?: ""
        val className = node.className?.toString() ?: ""

        // 检查是否包含淘宝相关关键词
        val keywords = listOf("打开淘宝", "淘宝", "打开", "进入", "启动")
        val hasKeyword = keywords.any { keyword ->
            text.contains(keyword, ignoreCase = true) ||
            contentDesc.contains(keyword, ignoreCase = true)
        }

        // 检查是否是可点击的元素
        val isClickable = node.isClickable ||
                         className.contains("Button", ignoreCase = true) ||
                         className.contains("View", ignoreCase = true)

        if (hasKeyword && isClickable) {
            candidates.add(node)
        }

        // 递归查找子节点
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                collectTaobaoButtonCandidates(child, candidates)
                child.recycle()
            }
        }
    }

    private fun calculateButtonScore(node: AccessibilityNodeInfo): Int {
        var score = 0
        val text = node.text?.toString() ?: ""
        val contentDesc = node.contentDescription?.toString() ?: ""
        val className = node.className?.toString() ?: ""

        // 文本匹配得分
        when {
            text.equals("打开淘宝", ignoreCase = true) -> score += 100
            text.contains("打开淘宝", ignoreCase = true) -> score += 80
            text.equals("淘宝", ignoreCase = true) -> score += 60
            text.contains("淘宝", ignoreCase = true) -> score += 40
            text.contains("打开", ignoreCase = true) -> score += 20
        }

        // 描述匹配得分
        when {
            contentDesc.contains("打开淘宝", ignoreCase = true) -> score += 50
            contentDesc.contains("淘宝", ignoreCase = true) -> score += 30
        }

        // 元素类型得分
        when {
            className.contains("Button", ignoreCase = true) -> score += 30
            node.isClickable -> score += 20
        }

        // 位置得分（下方的按钮通常更重要）
        val rect = android.graphics.Rect()
        node.getBoundsInScreen(rect)
        if (rect.top > 500) { // 如果按钮在屏幕下半部分
            score += 25
        }

        // 大小得分（较大的按钮通常更重要）
        val area = rect.width() * rect.height()
        if (area > 50000) { // 较大的按钮
            score += 15
        }

        return score
    }

    private fun findAnyTaobaoButton(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        // 原来的查找逻辑作为备选
        val text = node.text?.toString() ?: ""
        val contentDesc = node.contentDescription?.toString() ?: ""
        val className = node.className?.toString() ?: ""

        val keywords = listOf("淘宝", "打开")
        val hasKeyword = keywords.any { keyword ->
            text.contains(keyword, ignoreCase = true) ||
            contentDesc.contains(keyword, ignoreCase = true)
        }

        val isClickableElement = node.isClickable ||
                                className.contains("Button", ignoreCase = true) ||
                                className.contains("View", ignoreCase = true)

        if (hasKeyword && isClickableElement) {
            return node
        }

        // 递归查找子节点
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                val result = findAnyTaobaoButton(child)
                if (result != null) {
                    return result
                }
                child.recycle()
            }
        }

        return null
    }
    
    private fun clickNode(node: AccessibilityNodeInfo) {
        try {
            val text = node.text?.toString() ?: ""
            val desc = node.contentDescription?.toString() ?: ""
            val className = node.className?.toString() ?: ""

            FloatingWindowService.addLog("准备点击: $text|$desc|$className")

            // 获取节点位置信息
            val rect = Rect()
            node.getBoundsInScreen(rect)
            FloatingWindowService.addLog("按钮位置: (${rect.left}, ${rect.top}, ${rect.right}, ${rect.bottom})")

            // 方法1: 尝试直接点击
            if (node.isClickable) {
                FloatingWindowService.addLog("尝试直接点击...")
                val success = node.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                FloatingWindowService.addLog("直接点击结果: $success")
                if (success) {
                    // 等待一下看是否有反应
                    handler.postDelayed({
                        checkClickResult()
                    }, 1500)
                    return
                }
            } else {
                FloatingWindowService.addLog("节点不可直接点击")
            }

            // 方法2: 尝试点击父节点
            FloatingWindowService.addLog("尝试查找可点击的父节点...")
            var parent = node.parent
            var level = 1
            while (parent != null && level <= 3) { // 最多向上查找3层
                if (parent.isClickable) {
                    FloatingWindowService.addLog("找到可点击父节点(第${level}层)")
                    val success = parent.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                    FloatingWindowService.addLog("父节点点击结果: $success")
                    if (success) {
                        parent.recycle()
                        handler.postDelayed({
                            checkClickResult()
                        }, 1500)
                        return
                    }
                }
                val nextParent = parent.parent
                parent.recycle()
                parent = nextParent
                level++
            }
            parent?.recycle()

            // 方法3: 手势点击
            if (rect.isEmpty) {
                FloatingWindowService.addLog("节点边界为空，无法手势点击")
                return
            }

            val centerX = rect.centerX().toFloat()
            val centerY = rect.centerY().toFloat()

            FloatingWindowService.addLog("尝试手势点击坐标: (${centerX.toInt()}, ${centerY.toInt()})")

            val path = Path()
            path.moveTo(centerX, centerY)

            val gesture = GestureDescription.Builder()
                .addStroke(GestureDescription.StrokeDescription(path, 0, 150))
                .build()

            val result = dispatchGesture(gesture, object : GestureResultCallback() {
                override fun onCompleted(gestureDescription: GestureDescription?) {
                    FloatingWindowService.addLog("手势点击完成")
                    handler.postDelayed({
                        checkClickResult()
                    }, 1500)
                }

                override fun onCancelled(gestureDescription: GestureDescription?) {
                    FloatingWindowService.addLog("手势点击被取消")
                }
            }, null)

            FloatingWindowService.addLog("手势分发结果: $result")

        } catch (e: Exception) {
            FloatingWindowService.addLog("点击失败: ${e.message}")
        }
    }

    private fun checkClickResult() {
        try {
            val currentRoot = rootInActiveWindow
            if (currentRoot != null) {
                if (isInTaobaoApp(currentRoot)) {
                    FloatingWindowService.addLog("✅ 成功跳转到淘宝app")
                } else {
                    FloatingWindowService.addLog("❌ 未检测到跳转，点击可能无效")
                }
                currentRoot.recycle()
            } else {
                FloatingWindowService.addLog("无法检测跳转结果")
            }
        } catch (e: Exception) {
            FloatingWindowService.addLog("检测跳转结果失败: ${e.message}")
        }
    }
}
