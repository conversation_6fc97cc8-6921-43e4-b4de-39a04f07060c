package com.example.test001

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.graphics.Path
import android.graphics.Rect
import android.os.Handler
import android.os.Looper
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo

class AutoClickAccessibilityService : AccessibilityService() {
    
    private val handler = Handler(Looper.getMainLooper())
    private var isEnabled = false
    
    companion object {
        private var instance: AutoClickAccessibilityService? = null
        
        fun enableAutoClick() {
            instance?.isEnabled = true
            FloatingWindowService.addLog("无障碍服务已启用")
        }
        
        fun disableAutoClick() {
            instance?.isEnabled = false
            FloatingWindowService.addLog("无障碍服务已禁用")
        }

        fun isServiceRunning(): Bo<PERSON>an {
            return instance != null
        }

        fun manualCheck() {
            instance?.let {
                FloatingWindowService.addLog("手动触发检测")
                it.handler.post {
                    it.checkAndClickTaobaoButton()
                }
            }
        }
    }
    
    override fun onServiceConnected() {
        super.onServiceConnected()
        instance = this
        FloatingWindowService.addLog("无障碍服务已连接")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        instance = null
    }
    
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (!isEnabled || event == null) return

        // 记录事件信息用于调试
        val packageName = event.packageName?.toString() ?: ""

        when (event.eventType) {
            AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> {
                FloatingWindowService.addLog("窗口改变: $packageName")
                // 延迟检查，给页面加载时间
                handler.postDelayed({
                    checkAndClickTaobaoButton()
                }, 4000)
            }
            AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
                // 内容改变时也检查，但频率低一些
                if (System.currentTimeMillis() % 3 == 0L) { // 减少频率
                    handler.postDelayed({
                        checkAndClickTaobaoButton()
                    }, 2000)
                }
            }
        }
    }
    
    override fun onInterrupt() {
        // 服务被中断时的处理
    }
    
    private fun checkAndClickTaobaoButton() {
        if (!isEnabled) return

        try {
            val rootNode = rootInActiveWindow
            if (rootNode == null) {
                FloatingWindowService.addLog("无法获取根节点，可能权限不足")
                // 尝试重新获取
                handler.postDelayed({
                    val retryRoot = rootInActiveWindow
                    if (retryRoot != null) {
                        FloatingWindowService.addLog("重试获取根节点成功")
                        processRootNode(retryRoot)
                    } else {
                        FloatingWindowService.addLog("重试仍无法获取根节点")
                    }
                }, 1000)
                return
            }

            processRootNode(rootNode)

        } catch (e: Exception) {
            FloatingWindowService.addLog("检查过程出错: ${e.message}")
        }
    }

    private fun processRootNode(rootNode: AccessibilityNodeInfo) {
        try {
            FloatingWindowService.addLog("成功获取根节点，开始检查...")

            // 首先检查是否已经在淘宝app中
            if (isInTaobaoApp(rootNode)) {
                FloatingWindowService.addLog("已在淘宝app中，无需点击")
                return
            }

            // 查找"打开淘宝"按钮
            val taobaoButton = findTaobaoButton(rootNode)
            if (taobaoButton != null) {
                FloatingWindowService.addLog("找到打开淘宝按钮，准备点击")
                clickNode(taobaoButton)
                FloatingWindowService.addLog("已点击打开淘宝按钮")
            } else {
                FloatingWindowService.addLog("未找到打开淘宝按钮，查找所有按钮")
                findAllClickableElements(rootNode)
            }

        } finally {
            rootNode.recycle()
        }
    }

    private fun findAllClickableElements(node: AccessibilityNodeInfo) {
        // 查找所有可点击的元素
        val clickableElements = mutableListOf<AccessibilityNodeInfo>()
        findClickableElementsRecursive(node, clickableElements)

        if (clickableElements.isNotEmpty()) {
            FloatingWindowService.addLog("找到${clickableElements.size}个可点击元素")

            // 尝试点击包含关键词的元素
            for (element in clickableElements) {
                val text = element.text?.toString() ?: ""
                val desc = element.contentDescription?.toString() ?: ""
                val combined = "$text|$desc"

                if (combined.contains("淘宝", true) ||
                    combined.contains("打开", true) ||
                    combined.contains("进入", true) ||
                    combined.contains("启动", true)) {
                    FloatingWindowService.addLog("尝试点击元素: $combined")
                    clickNode(element)
                    break
                }
            }

            // 如果没找到关键词，尝试点击最大的按钮（通常是主要操作按钮）
            if (clickableElements.isNotEmpty()) {
                val largestButton = clickableElements.maxByOrNull { element ->
                    val rect = android.graphics.Rect()
                    element.getBoundsInScreen(rect)
                    rect.width() * rect.height()
                }

                largestButton?.let {
                    val text = it.text?.toString() ?: ""
                    val desc = it.contentDescription?.toString() ?: ""
                    FloatingWindowService.addLog("尝试点击最大按钮: $text|$desc")
                    clickNode(it)
                }
            }

            // 清理资源
            clickableElements.forEach { it.recycle() }
        } else {
            FloatingWindowService.addLog("页面中没有找到可点击元素")
        }
    }

    private fun findClickableElementsRecursive(node: AccessibilityNodeInfo, elements: MutableList<AccessibilityNodeInfo>) {
        if (node.isClickable) {
            val text = node.text?.toString() ?: ""
            val desc = node.contentDescription?.toString() ?: ""

            if (text.isNotEmpty() || desc.isNotEmpty()) {
                // 创建节点的副本，避免回收问题
                elements.add(node)
            }
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                findClickableElementsRecursive(child, elements)
                child.recycle()
            }
        }
    }
    
    private fun isInTaobaoApp(node: AccessibilityNodeInfo): Boolean {
        // 检查包名是否为淘宝相关
        val packageName = node.packageName?.toString() ?: ""
        return packageName.contains("taobao") || 
               packageName.contains("tmall") ||
               packageName.contains("alibaba")
    }
    
    private fun findTaobaoButton(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        // 检查当前节点的文本
        val text = node.text?.toString() ?: ""
        val contentDesc = node.contentDescription?.toString() ?: ""
        val className = node.className?.toString() ?: ""

        // 更宽泛的关键词匹配
        val keywords = listOf("打开淘宝", "淘宝", "打开", "进入", "启动", "跳转")
        val hasKeyword = keywords.any { keyword ->
            text.contains(keyword, ignoreCase = true) ||
            contentDesc.contains(keyword, ignoreCase = true)
        }

        // 检查是否是可点击的元素
        val isClickableElement = node.isClickable ||
                                className.contains("Button", ignoreCase = true) ||
                                className.contains("View", ignoreCase = true) ||
                                className.contains("Text", ignoreCase = true)

        if (hasKeyword && isClickableElement) {
            FloatingWindowService.addLog("找到匹配按钮: $text|$contentDesc")
            return node
        }

        // 递归查找子节点
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                val result = findTaobaoButton(child)
                if (result != null) {
                    return result
                }
                child.recycle()
            }
        }

        return null
    }
    
    private fun clickNode(node: AccessibilityNodeInfo) {
        // 尝试直接点击
        if (node.isClickable) {
            node.performAction(AccessibilityNodeInfo.ACTION_CLICK)
            return
        }
        
        // 如果不能直接点击，尝试手势点击
        val rect = Rect()
        node.getBoundsInScreen(rect)
        
        val centerX = rect.centerX().toFloat()
        val centerY = rect.centerY().toFloat()
        
        val path = Path()
        path.moveTo(centerX, centerY)
        
        val gesture = GestureDescription.Builder()
            .addStroke(GestureDescription.StrokeDescription(path, 0, 100))
            .build()
        
        dispatchGesture(gesture, null, null)
    }
}
