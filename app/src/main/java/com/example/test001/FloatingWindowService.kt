package com.example.test001

import android.app.Service
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import androidx.core.content.ContextCompat

class FloatingWindowService : Service() {
    
    private var windowManager: WindowManager? = null
    private var floatingView: View? = null
    private var statusText: TextView? = null
    private var logText: TextView? = null
    private var stopButton: Button? = null
    
    companion object {
        private var instance: FloatingWindowService? = null
        
        fun updateStatus(status: String) {
            instance?.updateStatusText(status)
        }
        
        fun addLog(log: String) {
            instance?.addLogText(log)
        }
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        createFloatingWindow()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        instance = null
        floatingView?.let { windowManager?.removeView(it) }
    }
    
    private fun createFloatingWindow() {
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        
        // 创建悬浮窗布局
        floatingView = LayoutInflater.from(this).inflate(R.layout.floating_window, null)
        
        statusText = floatingView?.findViewById(R.id.statusText)
        logText = floatingView?.findViewById(R.id.logText)
        stopButton = floatingView?.findViewById(R.id.stopButton)

        // 设置停止按钮点击事件
        stopButton?.setOnClickListener {
            // 发送停止脚本的广播
            val stopIntent = Intent("com.example.test001.STOP_SCRIPT")
            sendBroadcast(stopIntent)
        }
        
        // 设置悬浮窗参数
        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                WindowManager.LayoutParams.TYPE_PHONE
            },
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        )
        
        params.gravity = Gravity.TOP or Gravity.START
        params.x = 0
        params.y = 100
        
        // 添加拖拽功能
        var initialX = 0
        var initialY = 0
        var initialTouchX = 0f
        var initialTouchY = 0f
        
        floatingView?.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    initialX = params.x
                    initialY = params.y
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    params.x = initialX + (event.rawX - initialTouchX).toInt()
                    params.y = initialY + (event.rawY - initialTouchY).toInt()
                    windowManager?.updateViewLayout(floatingView, params)
                    true
                }
                else -> false
            }
        }
        
        // 添加悬浮窗到窗口管理器
        windowManager?.addView(floatingView, params)
        
        updateStatusText("悬浮窗已启动")
    }
    
    private fun updateStatusText(status: String) {
        statusText?.text = "状态: $status"
    }
    
    private fun addLogText(log: String) {
        val currentTime = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault())
            .format(java.util.Date())
        val newLog = "[$currentTime] $log"
        
        val currentLogs = logText?.text?.toString() ?: ""
        val logs = currentLogs.split("\n").toMutableList()
        
        logs.add(newLog)
        
        // 只保留最近10条日志
        if (logs.size > 10) {
            logs.removeAt(0)
        }
        
        logText?.text = logs.joinToString("\n")
    }
}
