package com.example.test001

import android.app.Service
import android.content.Context
import android.content.Intent
import android.media.AudioManager
import android.os.IBinder
import android.os.Handler
import android.os.Looper

class VolumeKeyService : Service() {
    
    private var audioManager: AudioManager? = null
    private var originalVolume = 0
    private val handler = Handler(Looper.getMainLooper())
    private var lastVolumeChangeTime = 0L
    
    companion object {
        private var onVolumeKeyPressed: (() -> Unit)? = null
        
        fun setVolumeKeyListener(listener: () -> Unit) {
            onVolumeKeyPressed = listener
        }
        
        fun removeVolumeKeyListener() {
            onVolumeKeyPressed = null
        }
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onCreate() {
        super.onCreate()
        audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
        originalVolume = audioManager?.getStreamVolume(AudioManager.STREAM_MUSIC) ?: 0
        startVolumeMonitoring()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        stopVolumeMonitoring()
    }
    
    private fun startVolumeMonitoring() {
        // 由于Android限制，我们使用一个替代方案
        // 在悬浮窗中添加音量键监听提示
        FloatingWindowService.addLog("音量键监听已启动")
    }
    
    private fun stopVolumeMonitoring() {
        FloatingWindowService.addLog("音量键监听已停止")
    }
}
