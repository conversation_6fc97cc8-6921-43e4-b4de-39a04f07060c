package com.example.test001

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.view.KeyEvent
import android.widget.Toast
import android.text.TextUtils.SimpleStringSplitter
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

import com.example.test001.ui.theme.Test001Theme
import kotlin.random.Random

class MainActivity : ComponentActivity() {

    // 预设的网址列表
    private val urlList = listOf(
        "https://web.m.taobao.com/app/tnode/web/index?ut_sk=1.YZ2zhQSDuugDAD/ZFzFjmRl0_21380790_1754285885835.Copy.guangguang&extParams=%7B%22contentId%22%3A%22528822603042%22%2C%22sceneSource%22%3A%22guang_share_shipin%22%7D&tabid=video&sourceType=other&tnode=page_guangguanghome",
        "https://web.m.taobao.com/app/tnode/web/index?ut_sk=1.YZ2zhQSDuugDAD/ZFzFjmRl0_21380790_1754285885835.Copy.guangguang&extParams=%7B%22contentId%22%3A%22528815724621%22%2C%22sceneSource%22%3A%22guang_share_shipin%22%7D&tabid=video&sourceType=other&tnode=page_guangguanghome",
        "https://web.m.taobao.com/app/tnode/web/index?ut_sk=1.YZ2zhQSDuugDAD/ZFzFjmRl0_21380790_1754285885835.Copy.guangguang&extParams=%7B%22contentId%22%3A%22528774222290%22%2C%22sceneSource%22%3A%22guang_share_shipin%22%7D&tabid=video&sourceType=other&tnode=page_guangguanghome"
    )

    // 脚本控制变量
    private var isRunning = false
    private var currentIndex = 0
    private val handler = Handler(Looper.getMainLooper())
    private var currentRunnable: Runnable? = null

    // UI状态
    private var statusText = mutableStateOf("准备就绪")
    private var currentUrl = mutableStateOf("")
    private var remainingTime = mutableStateOf(0)

    // 广播接收器
    private val stopScriptReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == "com.example.test001.STOP_SCRIPT") {
                stopScript()
                Toast.makeText(this@MainActivity, "脚本已通过悬浮窗停止", Toast.LENGTH_SHORT).show()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 注册广播接收器
        val filter = IntentFilter("com.example.test001.STOP_SCRIPT")
        registerReceiver(stopScriptReceiver, filter, RECEIVER_NOT_EXPORTED)

        setContent {
            Test001Theme {
                AutoScriptApp()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 注销广播接收器
        unregisterReceiver(stopScriptReceiver)
        // 确保停止脚本
        if (isRunning) {
            stopScript()
        }
    }

    // 监听音量键停止脚本
    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        when (keyCode) {
            KeyEvent.KEYCODE_VOLUME_UP,
            KeyEvent.KEYCODE_VOLUME_DOWN -> {
                if (isRunning) {
                    stopScript()
                    Toast.makeText(this, "脚本已停止", Toast.LENGTH_SHORT).show()
                    return true
                }
            }
        }
        return super.onKeyDown(keyCode, event)
    }

    // 开始脚本
    private fun startScript() {
        if (isRunning) return

        // 检查悬浮窗权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
            Toast.makeText(this, "请先授予悬浮窗权限", Toast.LENGTH_LONG).show()
            val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:$packageName"))
            startActivity(intent)
            return
        }

        // 检查无障碍服务
        if (!isAccessibilityServiceEnabled()) {
            Toast.makeText(this, "请先启用无障碍服务以实现自动点击", Toast.LENGTH_LONG).show()
            openAccessibilitySettings()
            return
        }

        isRunning = true
        currentIndex = 0
        statusText.value = "脚本运行中..."

        // 启动悬浮窗服务
        val serviceIntent = Intent(this, FloatingWindowService::class.java)
        startService(serviceIntent)

        // 启用无障碍自动点击
        AutoClickAccessibilityService.enableAutoClick()

        FloatingWindowService.updateStatus("脚本已启动")
        FloatingWindowService.addLog("开始执行自动化脚本")
        FloatingWindowService.addLog("无障碍服务已启用自动点击")

        executeNextStep()
    }

    // 停止脚本
    private fun stopScript() {
        isRunning = false
        currentRunnable?.let { handler.removeCallbacks(it) }
        statusText.value = "脚本已停止"
        currentUrl.value = ""
        remainingTime.value = 0

        // 禁用无障碍自动点击
        AutoClickAccessibilityService.disableAutoClick()

        // 停止悬浮窗服务
        val serviceIntent = Intent(this, FloatingWindowService::class.java)
        stopService(serviceIntent)

        FloatingWindowService.addLog("脚本已停止")
        FloatingWindowService.addLog("无障碍服务已禁用")
    }

    // 执行下一步
    private fun executeNextStep() {
        if (!isRunning) return

        val url = urlList[currentIndex]
        currentUrl.value = url

        // 使用系统浏览器打开网址
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
            startActivity(intent)

            statusText.value = "已打开浏览器，等待用户操作..."
            FloatingWindowService.updateStatus("已打开第${currentIndex + 1}个网址")
            FloatingWindowService.addLog("打开网址 ${currentIndex + 1}/3")

            // 固定等待30秒
            val waitTime = 30
            remainingTime.value = waitTime

            FloatingWindowService.addLog("等待30秒后自动打开下一个")

            // 开始倒计时
            startCountdown(waitTime)

        } catch (e: Exception) {
            // 如果网址打不开，跳过到下一个
            Toast.makeText(this, "网址打开失败，跳过: $url", Toast.LENGTH_SHORT).show()
            FloatingWindowService.addLog("网址打开失败，跳过")
            moveToNext()
        }
    }

    // 倒计时
    private fun startCountdown(seconds: Int) {
        if (seconds <= 0 || !isRunning) {
            moveToNext()
            return
        }

        remainingTime.value = seconds
        FloatingWindowService.updateStatus("等待中，剩余${seconds}秒")

        currentRunnable = Runnable {
            startCountdown(seconds - 1)
        }
        handler.postDelayed(currentRunnable!!, 1000)
    }

    // 移动到下一个网址
    private fun moveToNext() {
        if (!isRunning) return

        currentIndex = (currentIndex + 1) % urlList.size

        if (currentIndex == 0) {
            FloatingWindowService.addLog("完成一轮循环，重新开始")
        }

        FloatingWindowService.addLog("准备打开下一个网址...")

        // 短暂延迟后执行下一步
        currentRunnable = Runnable {
            executeNextStep()
        }
        handler.postDelayed(currentRunnable!!, 2000) // 增加到2秒延迟
    }

    // 检查无障碍服务是否启用
    private fun isAccessibilityServiceEnabled(): Boolean {
        val accessibilityEnabled = try {
            Settings.Secure.getInt(
                contentResolver,
                Settings.Secure.ACCESSIBILITY_ENABLED
            )
        } catch (e: Settings.SettingNotFoundException) {
            0
        }

        if (accessibilityEnabled == 1) {
            val services = Settings.Secure.getString(
                contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            )

            if (!services.isNullOrEmpty()) {
                val splitter = SimpleStringSplitter(':')
                splitter.setString(services)

                while (splitter.hasNext()) {
                    val service = splitter.next()
                    if (service.contains("AutoClickAccessibilityService")) {
                        return true
                    }
                }
            }
        }

        return false
    }

    // 打开无障碍设置
    private fun openAccessibilitySettings() {
        val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
        startActivity(intent)
        Toast.makeText(this, "请找到并启用 '淘宝自动化脚本' 无障碍服务", Toast.LENGTH_LONG).show()
    }

    @Composable
    fun AutoScriptApp() {
        Scaffold(
            modifier = Modifier.fillMaxSize()
        ) { innerPadding ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(innerPadding)
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {

                // 标题
                Text(
                    text = "淘宝自动化脚本",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 32.dp)
                )

                // 状态显示
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "状态: ${statusText.value}",
                            fontSize = 16.sp,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )

                        if (currentUrl.value.isNotEmpty()) {
                            Text(
                                text = "正在处理第 ${currentIndex + 1} 个网址",
                                fontSize = 14.sp,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )
                        }

                        if (remainingTime.value > 0) {
                            Text(
                                text = "剩余时间: ${remainingTime.value}秒",
                                fontSize = 14.sp,
                                color = Color.Blue
                            )
                        }

                        // 服务状态显示
                        Text(
                            text = "悬浮窗权限: ${if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && Settings.canDrawOverlays(this@MainActivity)) "✓" else "✗"}",
                            fontSize = 12.sp,
                            color = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && Settings.canDrawOverlays(this@MainActivity)) Color.Green else Color.Red,
                            modifier = Modifier.padding(top = 4.dp)
                        )

                        Text(
                            text = "无障碍服务: ${if (isAccessibilityServiceEnabled()) "✓" else "✗"}",
                            fontSize = 12.sp,
                            color = if (isAccessibilityServiceEnabled()) Color.Green else Color.Red,
                            modifier = Modifier.padding(top = 2.dp)
                        )
                    }
                }

                // 控制按钮
                Row(
                    horizontalArrangement = Arrangement.spacedBy(16.dp),
                    modifier = Modifier.padding(top = 24.dp)
                ) {
                    Button(
                        onClick = { startScript() },
                        enabled = !isRunning,
                        modifier = Modifier
                            .weight(1f)
                            .height(56.dp)
                    ) {
                        Text("开始脚本", fontSize = 16.sp)
                    }

                    Button(
                        onClick = { stopScript() },
                        enabled = isRunning,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.Red
                        ),
                        modifier = Modifier
                            .weight(1f)
                            .height(56.dp)
                    ) {
                        Text("停止脚本", fontSize = 16.sp)
                    }
                }

                // 提示信息
                Text(
                    text = "提示: 按音量键可快速停止脚本",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(top = 24.dp)
                )

                // 使用说明
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "使用说明:",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )
                        Text(
                            text = "1. 点击开始脚本\n2. 系统浏览器会自动打开网页\n3. 手动点击\"打开淘宝\"按钮\n4. 等待计时完成后自动打开下一个",
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                    }
                }
            }
        }
    }
}