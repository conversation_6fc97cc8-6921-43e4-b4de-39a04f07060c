package com.example.test001

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.KeyEvent
import android.webkit.WebView
import android.webkit.WebViewClient
import android.webkit.WebSettings
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import com.example.test001.ui.theme.Test001Theme
import kotlin.random.Random

class MainActivity : ComponentActivity() {

    // 预设的网址列表
    private val urlList = listOf(
        "https://web.m.taobao.com/app/tnode/web/index?ut_sk=1.YZ2zhQSDuugDAD/ZFzFjmRl0_21380790_1754285885835.Copy.guangguang&extParams=%7B%22contentId%22%3A%22528822603042%22%2C%22sceneSource%22%3A%22guang_share_shipin%22%7D&tabid=video&sourceType=other&tnode=page_guangguanghome",
        "https://web.m.taobao.com/app/tnode/web/index?ut_sk=1.YZ2zhQSDuugDAD/ZFzFjmRl0_21380790_1754285885835.Copy.guangguang&extParams=%7B%22contentId%22%3A%22528822603042%22%2C%22sceneSource%22%3A%22guang_share_shipin%22%7D&tabid=video&sourceType=other&tnode=page_guangguanghome",
        "https://web.m.taobao.com/app/tnode/web/index?ut_sk=1.YZ2zhQSDuugDAD/ZFzFjmRl0_21380790_1754285885835.Copy.guangguang&extParams=%7B%22contentId%22%3A%22528822603042%22%2C%22sceneSource%22%3A%22guang_share_shipin%22%7D&tabid=video&sourceType=other&tnode=page_guangguanghome"
    )

    // 脚本控制变量
    private var isRunning = false
    private var currentIndex = 0
    private val handler = Handler(Looper.getMainLooper())
    private var currentRunnable: Runnable? = null

    // UI状态
    private var statusText = mutableStateOf("准备就绪")
    private var currentUrl = mutableStateOf("")
    private var remainingTime = mutableStateOf(0)

    // WebView相关
    private var webView: WebView? = null
    private var isPageLoaded = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            Test001Theme {
                AutoScriptApp()
            }
        }
    }

    // 监听音量键停止脚本
    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        when (keyCode) {
            KeyEvent.KEYCODE_VOLUME_UP,
            KeyEvent.KEYCODE_VOLUME_DOWN -> {
                if (isRunning) {
                    stopScript()
                    Toast.makeText(this, "脚本已停止", Toast.LENGTH_SHORT).show()
                    return true
                }
            }
        }
        return super.onKeyDown(keyCode, event)
    }

    // 开始脚本
    private fun startScript() {
        if (isRunning) return

        isRunning = true
        currentIndex = 0
        statusText.value = "脚本运行中..."

        executeNextStep()
    }

    // 停止脚本
    private fun stopScript() {
        isRunning = false
        currentRunnable?.let { handler.removeCallbacks(it) }
        statusText.value = "脚本已停止"
        currentUrl.value = ""
        remainingTime.value = 0
    }

    // 执行下一步
    private fun executeNextStep() {
        if (!isRunning) return

        val url = urlList[currentIndex]
        currentUrl.value = url
        isPageLoaded = false

        // 使用WebView加载网址
        try {
            webView?.loadUrl(url)
            statusText.value = "正在加载网页..."

        } catch (e: Exception) {
            // 如果网址打不开，跳过到下一个
            Toast.makeText(this, "网址打开失败，跳过: $url", Toast.LENGTH_SHORT).show()
            moveToNext()
        }
    }

    // 倒计时
    private fun startCountdown(seconds: Int) {
        if (seconds <= 0 || !isRunning) {
            moveToNext()
            return
        }

        remainingTime.value = seconds

        currentRunnable = Runnable {
            startCountdown(seconds - 1)
        }
        handler.postDelayed(currentRunnable!!, 1000)
    }

    // 移动到下一个网址
    private fun moveToNext() {
        if (!isRunning) return

        currentIndex = (currentIndex + 1) % urlList.size

        // 短暂延迟后执行下一步
        currentRunnable = Runnable {
            executeNextStep()
        }
        handler.postDelayed(currentRunnable!!, 1000)
    }

    // 配置WebView
    private fun setupWebView(webView: WebView) {
        this.webView = webView

        // WebView设置
        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            loadWithOverviewMode = true
            useWideViewPort = true
            builtInZoomControls = false
            displayZoomControls = false
            setSupportZoom(false)
            cacheMode = WebSettings.LOAD_DEFAULT
            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        }

        // 设置WebViewClient
        webView.webViewClient = object : WebViewClient() {
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                if (!isRunning) return

                isPageLoaded = true
                statusText.value = "页面加载完成，等待自动点击..."

                // 页面加载完成后，延迟2秒自动点击"打开淘宝"按钮
                handler.postDelayed({
                    if (isRunning && isPageLoaded) {
                        autoClickTaobaoButton()
                    }
                }, 2000)
            }

            override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                // 如果是淘宝app链接，尝试打开淘宝app
                if (url?.contains("taobao://") == true || url?.contains("tbopen://") == true) {
                    try {
                        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                        startActivity(intent)

                        // 打开淘宝app后，开始倒计时
                        statusText.value = "已打开淘宝，开始计时..."
                        val waitTime = Random.nextInt(20, 31)
                        remainingTime.value = waitTime
                        startCountdown(waitTime)

                        return true
                    } catch (e: Exception) {
                        Toast.makeText(this@MainActivity, "无法打开淘宝app", Toast.LENGTH_SHORT).show()
                    }
                }
                return false
            }
        }
    }

    // 自动点击"打开淘宝"按钮
    private fun autoClickTaobaoButton() {
        if (!isRunning) return

        val javascript = """
            (function() {
                // 查找"打开淘宝"按钮的多种可能选择器
                var selectors = [
                    'button:contains("打开淘宝")',
                    'a:contains("打开淘宝")',
                    '[class*="open"]:contains("淘宝")',
                    '[class*="button"]:contains("打开")',
                    'button[class*="primary"]',
                    '.btn-primary',
                    '[onclick*="taobao"]',
                    '[href*="taobao://"]',
                    '[href*="tbopen://"]'
                ];

                var clicked = false;

                // 尝试每个选择器
                for (var i = 0; i < selectors.length && !clicked; i++) {
                    try {
                        var elements = document.querySelectorAll(selectors[i]);
                        for (var j = 0; j < elements.length; j++) {
                            var element = elements[j];
                            if (element.offsetParent !== null) { // 确保元素可见
                                element.click();
                                clicked = true;
                                console.log('Clicked element with selector: ' + selectors[i]);
                                break;
                            }
                        }
                    } catch (e) {
                        console.log('Error with selector ' + selectors[i] + ': ' + e);
                    }
                }

                // 如果没有找到按钮，尝试点击页面中心
                if (!clicked) {
                    var centerX = window.innerWidth / 2;
                    var centerY = window.innerHeight / 2;
                    var element = document.elementFromPoint(centerX, centerY);
                    if (element) {
                        element.click();
                        console.log('Clicked center element');
                    }
                }

                return clicked;
            })();
        """

        webView?.evaluateJavascript(javascript) { result ->
            if (result == "true") {
                statusText.value = "已点击打开淘宝按钮"
            } else {
                statusText.value = "未找到按钮，尝试点击页面中心"
                // 如果自动点击失败，3秒后继续下一个
                handler.postDelayed({
                    if (isRunning) {
                        moveToNext()
                    }
                }, 3000)
            }
        }
    }

    @Composable
    fun AutoScriptApp() {
        Scaffold(
            modifier = Modifier.fillMaxSize()
        ) { innerPadding ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(innerPadding)
                    .padding(16.dp)
            ) {

                // 标题
                Text(
                    text = "淘宝自动化脚本",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // WebView显示区域
                if (isRunning) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(300.dp)
                            .padding(bottom = 16.dp),
                        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                    ) {
                        AndroidView(
                            factory = { context ->
                                WebView(context).apply {
                                    setupWebView(this)
                                }
                            },
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                }

                // 状态显示
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "状态: ${statusText.value}",
                            fontSize = 16.sp,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )

                        if (currentUrl.value.isNotEmpty()) {
                            Text(
                                text = "当前网址: ${currentUrl.value.take(50)}...",
                                fontSize = 12.sp,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )
                        }

                        if (remainingTime.value > 0) {
                            Text(
                                text = "剩余时间: ${remainingTime.value}秒",
                                fontSize = 14.sp,
                                color = Color.Blue
                            )
                        }
                    }
                }

                // 网址列表显示
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 24.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "网址列表:",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )

                        urlList.forEachIndexed { index, url ->
                            Text(
                                text = "${index + 1}. $url",
                                fontSize = 14.sp,
                                color = if (index == currentIndex && isRunning) Color.Red else Color.Gray,
                                modifier = Modifier.padding(vertical = 2.dp)
                            )
                        }
                    }
                }

                // 控制按钮
                Row(
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Button(
                        onClick = { startScript() },
                        enabled = !isRunning,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("开始脚本")
                    }

                    Button(
                        onClick = { stopScript() },
                        enabled = isRunning,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.Red
                        ),
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("停止脚本")
                    }
                }

                // 提示信息
                Text(
                    text = "提示: 按音量键可快速停止脚本",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(top = 16.dp)
                )
            }
        }
    }
}