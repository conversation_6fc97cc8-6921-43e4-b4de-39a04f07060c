<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="300dp"
    android:layout_height="wrap_content"
    android:background="@drawable/floating_window_background"
    android:orientation="vertical"
    android:padding="12dp">

    <TextView
        android:id="@+id/statusText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="状态: 准备中"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:background="#33000000"
        android:padding="8dp">

        <TextView
            android:id="@+id/logText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="日志记录:\n等待开始..."
            android:textColor="#CCFFFFFF"
            android:textSize="12sp"
            android:fontFamily="monospace" />

    </ScrollView>

    <Button
        android:id="@+id/stopButton"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginTop="8dp"
        android:text="停止脚本"
        android:textSize="12sp"
        android:backgroundTint="#FFFF4444" />

</LinearLayout>
